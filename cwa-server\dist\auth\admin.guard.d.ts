import { CanActivate, ExecutionContext } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Model } from 'mongoose';
import { IUser } from 'src/users/interfaces/users.interface';
export declare class AdminGuard implements CanActivate {
    private readonly jwtService;
    private userModel;
    constructor(jwtService: JwtService, userModel: Model<IUser>);
    canActivate(context: ExecutionContext): Promise<boolean>;
}
