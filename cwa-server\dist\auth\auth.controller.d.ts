import { AuthService } from "./auth.service";
import { CreateUserDto } from "src/users/dto/createUser.dto";
import { signInDto } from "./dto/signIn.dto";
import { ForgotPasswordDto } from "./dto/forget-password.dto";
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ChangePasswordDto } from "./dto/change-password.dto";
import { VerifyOtpDTO } from "./dto/verify-otp.dto";
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    signUp(signUpDto: CreateUserDto): Promise<{
        status: string;
        data: {
            user: {
                id: string;
                name: string;
                email: string;
                phone: string;
            };
            token: string;
        };
    }>;
    signIn(signInDto: signInDto): Promise<{
        status: string;
        data: {
            user: {
                id: string;
                name: string;
                email: string;
                phone: string;
            };
            token: string;
        };
    }>;
    forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<{
        status: string;
        message: string;
    }>;
    verifyOtp(verifyOtpDto: VerifyOtpDTO): Promise<{
        status: string;
        message: string;
        resetToken?: string;
    }>;
    resetPassword(resetPasswordDto: ResetPasswordDto): Promise<{
        status: string;
        message: string;
    }>;
    changePassword(req: any, changePasswordDto: ChangePasswordDto): Promise<{
        status: string;
        message: string;
    }>;
}
