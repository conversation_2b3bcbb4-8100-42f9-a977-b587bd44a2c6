import { JwtService } from '@nestjs/jwt';
import { UsersService } from 'src/users/users.service';
import { IAuth } from './interfaces/auth.interfaces';
import { IUser } from 'src/users/interfaces/users.interface';
import { CreateUserDto } from 'src/users/dto/createUser.dto';
import { signInDto } from './dto/signIn.dto';
import { OtpService } from 'src/otp/otp.service';
import { ForgotPasswordDto } from './dto/forget-password.dto';
import { MailerService } from '@nestjs-modules/mailer';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { Model } from 'mongoose';
import { VerifyOtpDTO } from './dto/verify-otp.dto';
export declare class AuthService {
    private userModel;
    private readonly usersService;
    private readonly jwtService;
    private readonly otpService;
    private readonly mailerService;
    constructor(userModel: Model<IUser>, usersService: UsersService, jwtService: JwtService, otpService: OtpService, mailerService: MailerService);
    signUp(signUpDto: CreateUserDto): Promise<IAuth>;
    signIn(signInDto: signInDto): Promise<IAuth>;
    forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<{
        message: string;
    }>;
    verifyOtp(verifyOtpDto: VerifyOtpDTO): Promise<any>;
    resetPassword(dto: ResetPasswordDto): Promise<any>;
    changePassword(userId: string, changePasswordDto: ChangePasswordDto): Promise<{
        message: string;
    }>;
}
