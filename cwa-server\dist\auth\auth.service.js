"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const users_service_1 = require("../users/users.service");
const bcrypt = require("bcrypt");
const otp_service_1 = require("../otp/otp.service");
const otpHelper_1 = require("../ultils/otpHelper");
const mailer_1 = require("@nestjs-modules/mailer");
const mongoose_1 = require("@nestjs/mongoose");
const users_schema_1 = require("../users/schema/users.schema");
const mongoose_2 = require("mongoose");
let AuthService = class AuthService {
    constructor(userModel, usersService, jwtService, otpService, mailerService) {
        this.userModel = userModel;
        this.usersService = usersService;
        this.jwtService = jwtService;
        this.otpService = otpService;
        this.mailerService = mailerService;
    }
    async signUp(signUpDto) {
        const user = await this.usersService.createUser(signUpDto);
        const payload = { name: user.name, _id: user._id };
        const accessToken = await this.jwtService.signAsync(payload);
        return {
            ...user,
            accessToken,
        };
    }
    async signIn(signInDto) {
        const user = await this.usersService.findByEmail(signInDto.email.toLowerCase());
        if (!user) {
            throw new Error('Invalid credentials');
        }
        const userWithPassword = await this.usersService.findByEmailWithPassword(signInDto.email.toLowerCase());
        const isMatch = await bcrypt.compare(signInDto.password, userWithPassword.password);
        if (!isMatch) {
            throw new Error('Invalid credentials');
        }
        const payload = { name: user.name, _id: user._id };
        const accessToken = await this.jwtService.signAsync(payload);
        const userObject = user.toObject ? user.toObject() : user;
        return {
            ...userObject,
            accessToken,
        };
    }
    async forgotPassword(forgotPasswordDto) {
        try {
            const email = forgotPasswordDto.email.toLowerCase();
            const user = await this.usersService.findByEmail(email);
            if (!user) {
                throw new common_1.HttpException({
                    status: common_1.HttpStatus.BAD_REQUEST,
                    message: 'Invalid Email',
                }, common_1.HttpStatus.BAD_REQUEST);
            }
            const otp = (0, otpHelper_1.otpgenerator)();
            const opt = await this.otpService.create({
                email: user.email,
                otp: otp.toString(),
            });
            console.log('otp', opt);
            await this.mailerService.sendMail({
                to: email,
                subject: 'Reset Password OTP',
                text: `Your OTP for resetting password is ${otp}.`,
            });
            return { message: `OTP sent to ${email}` };
        }
        catch (error) {
            console.error('Error in forgotPassword:', error);
            throw new common_1.HttpException({
                status: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
                message: 'Something went wrong. Please try again later.',
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async verifyOtp(verifyOtpDto) {
        const email = verifyOtpDto.email.toLowerCase();
        const otpFound = await this.otpService.findOne(email, verifyOtpDto.otp);
        if (!otpFound) {
            throw new common_1.HttpException({
                status: common_1.HttpStatus.BAD_REQUEST,
                message: 'Invalid OTP',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
        this.otpService.delete(otpFound._id);
        return {
            message: 'OTP Verified',
            status: 200,
        };
    }
    async resetPassword(dto) {
        const email = dto.email.toLowerCase();
        const user = await this.usersService.findByEmail(email);
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        await this.usersService.updatePassword(user._id, dto.password);
        return {
            message: 'PASSWORD RESTORED - 200',
        };
    }
    async changePassword(userId, changePasswordDto) {
        const { oldPassword, newPassword } = changePasswordDto;
        if (!oldPassword || !newPassword) {
            throw new common_1.BadRequestException('Old password and new password are required');
        }
        const user = await this.userModel.findById(userId).select('+password');
        if (!user) {
            throw new common_1.NotFoundException(`User not found with ID: ${userId}`);
        }
        if (!user.password) {
            throw new common_1.InternalServerErrorException('Password field is missing for the user');
        }
        const isOldPasswordValid = await bcrypt.compare(oldPassword, user.password);
        if (!isOldPasswordValid) {
            throw new common_1.UnauthorizedException('Invalid old password');
        }
        const hashedNewPassword = await bcrypt.hash(newPassword, 10);
        await this.userModel.findByIdAndUpdate(userId, { password: hashedNewPassword }, { new: true });
        return { message: 'Password changed successfully' };
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(users_schema_1.Users.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        users_service_1.UsersService,
        jwt_1.JwtService,
        otp_service_1.OtpService,
        mailer_1.MailerService])
], AuthService);
//# sourceMappingURL=auth.service.js.map