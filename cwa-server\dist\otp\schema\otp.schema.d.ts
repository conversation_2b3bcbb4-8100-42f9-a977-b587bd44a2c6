import * as mongoose from 'mongoose';
export declare class Otp extends mongoose.Document {
    email: string;
    otp: string;
    createdAt: Date;
}
export declare const OtpSchema: mongoose.Schema<Otp, mongoose.Model<Otp, any, any, any, mongoose.Document<unknown, any, Otp, any> & Otp & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, mongoose.DefaultSchemaOptions, Otp, mongoose.Document<unknown, {}, mongoose.FlatRecord<Otp>, {}> & mongoose.FlatRecord<Otp> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
