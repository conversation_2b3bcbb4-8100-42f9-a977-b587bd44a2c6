"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AllExceptionsFilter = void 0;
const common_1 = require("@nestjs/common");
let AllExceptionsFilter = class AllExceptionsFilter {
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        const status = exception instanceof common_1.HttpException
            ? exception.getStatus()
            : common_1.HttpStatus.INTERNAL_SERVER_ERROR;
        let errorMessage = 'Internal server error';
        let errors = [];
        if (exception instanceof common_1.HttpException) {
            const responseMessage = this.getErrorMessage(exception);
            if (Array.isArray(responseMessage)) {
                errors = responseMessage;
                errorMessage = 'Validation failed';
            }
            else {
                errors.push(responseMessage);
                errorMessage = responseMessage;
            }
        }
        else if (exception instanceof Error) {
            errorMessage = exception.message;
            errors.push(exception.message);
        }
        if (exception instanceof Error &&
            exception.message.includes('E11000 duplicate key error')) {
            errorMessage = 'A duplicate entry already exists.';
            errors = ['A duplicate entry already exists.'];
        }
        response.status(status).json({
            meta: {
                statusCode: status,
                status: 'error',
                message: errorMessage,
                errors: errors.length > 0 ? errors : null,
                timestamp: new Date().toISOString(),
                path: request.url,
            },
            data: null,
        });
    }
    getErrorMessage(exception) {
        const response = exception.getResponse();
        if (typeof response === 'string') {
            return response;
        }
        else if (typeof response === 'object' && 'message' in response) {
            return response['message'];
        }
        return 'Internal server error';
    }
};
exports.AllExceptionsFilter = AllExceptionsFilter;
exports.AllExceptionsFilter = AllExceptionsFilter = __decorate([
    (0, common_1.Catch)()
], AllExceptionsFilter);
//# sourceMappingURL=allExceptionsFilter.js.map