"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.otpgenerator = otpgenerator;
exports.isExpire = isExpire;
function otpgenerator() {
    return Math.floor(Math.random() * (9999 - 1000) + 1000);
}
function isExpire(expireDate) {
    const expireyDate = new Date(expireDate).getTime() + 3600 * 1000 * 5 + 60 * 1000;
    const currentDate = new Date().getTime();
    return expireyDate < currentDate;
}
//# sourceMappingURL=otpHelper.js.map