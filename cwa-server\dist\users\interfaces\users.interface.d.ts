export interface IUser {
    _id: string;
    name: string;
    email: string;
    password: string;
    phone: string;
    isAdmin: boolean;
    resetPasswordToken?: string;
    resetPasswordOTP?: string;
    resetPasswordExpires?: Date;
    firstName?: string;
    lastName?: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface IUserResponse {
    _id: string;
    name: string;
    email: string;
    phone: string;
    isAdmin: boolean;
    resetPasswordToken?: string;
    resetPasswordOTP?: string;
    resetPasswordExpires?: Date;
    firstName?: string;
    lastName?: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface IUserCreate {
    name: string;
    email: string;
    password: string;
    phone: string;
    isAdmin?: boolean;
}
export interface IUserUpdate {
    name?: string;
    email?: string;
    phone?: string;
    isAdmin?: boolean;
}
export interface IUserQuery {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export interface IUserPaginationResponse {
    users: IUserResponse[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
