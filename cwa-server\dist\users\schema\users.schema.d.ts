import * as mongoose from 'mongoose';
export declare class Users extends mongoose.Document {
    name: string;
    email: string;
    password: string;
    phone: string;
    isAdmin: boolean;
    resetPasswordToken: string;
    resetPasswordOTP: string;
    resetPasswordExpires: Date;
    firstName: string;
    lastName: string;
}
export declare const UsersSchema: mongoose.Schema<Users, mongoose.Model<Users, any, any, any, mongoose.Document<unknown, any, Users, any> & Users & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, mongoose.DefaultSchemaOptions, Users, mongoose.Document<unknown, {}, mongoose.FlatRecord<Users>, {}> & mongoose.FlatRecord<Users> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
