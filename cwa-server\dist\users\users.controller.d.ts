import { UsersService } from './users.service';
import { CreateUserDto } from './dto/createUser.dto';
import { UpdateUserDto } from './dto/updateUser.dto';
import { IUserResponse, IUserQuery } from './interfaces/users.interface';
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    createUser(createUserDto: CreateUserDto): Promise<IUserResponse>;
    getUsers(query: IUserQuery): Promise<IUserResponse[]>;
    getUser(id: string): Promise<IUserResponse>;
    updateUser(id: string, updateUserDto: UpdateUserDto): Promise<IUserResponse>;
    deleteUser(id: string): Promise<{
        message: string;
    }>;
}
