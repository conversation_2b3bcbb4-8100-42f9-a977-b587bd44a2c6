"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const jwt_1 = require("@nestjs/jwt");
const config_1 = require("@nestjs/config");
const users_controller_1 = require("./users.controller");
const users_service_1 = require("./users.service");
const users_schema_1 = require("./schema/users.schema");
const admin_guard_1 = require("../auth/admin.guard");
const auth_guard_1 = require("../auth/auth.guard");
let UsersModule = class UsersModule {
};
exports.UsersModule = UsersModule;
exports.UsersModule = UsersModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            mongoose_1.MongooseModule.forFeature([
                { name: users_schema_1.Users.name, schema: users_schema_1.UsersSchema }
            ]),
            (0, common_1.forwardRef)(() => jwt_1.JwtModule),
        ],
        controllers: [users_controller_1.UsersController],
        providers: [users_service_1.UsersService, admin_guard_1.AdminGuard, auth_guard_1.AuthGuard],
        exports: [users_service_1.UsersService, mongoose_1.MongooseModule],
    })
], UsersModule);
//# sourceMappingURL=users.module.js.map