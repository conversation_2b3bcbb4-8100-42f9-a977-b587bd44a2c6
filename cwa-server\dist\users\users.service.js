"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const bcrypt = require("bcrypt");
const users_schema_1 = require("./schema/users.schema");
let UsersService = class UsersService {
    constructor(userModel) {
        this.userModel = userModel;
    }
    async createUser(createUserDto) {
        try {
            const existingUser = await this.userModel.findOne({
                email: createUserDto.email.toLowerCase()
            });
            if (existingUser) {
                throw new common_1.ConflictException('User with this email already exists');
            }
            const saltRounds = 10;
            const hashedPassword = await bcrypt.hash(createUserDto.password, saltRounds);
            const userData = {
                ...createUserDto,
                email: createUserDto.email.toLowerCase(),
                password: hashedPassword,
                isAdmin: createUserDto.isAdmin || false,
            };
            const user = new this.userModel(userData);
            const savedUser = await user.save();
            const { password, ...userWithoutPassword } = savedUser.toObject();
            return userWithoutPassword;
        }
        catch (error) {
            if (error instanceof common_1.ConflictException) {
                throw error;
            }
            if (error.name === 'ValidationError') {
                const messages = Object.values(error.errors).map((val) => val.message);
                throw new common_1.BadRequestException(messages.join(', '));
            }
            if (error.code === 11000) {
                throw new common_1.ConflictException('User with this email already exists');
            }
            throw new common_1.InternalServerErrorException('Failed to create user');
        }
    }
    async findByEmail(email) {
        try {
            const user = await this.userModel
                .findOne({ email: email.toLowerCase() })
                .select('-password')
                .exec();
            return user;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to find user by email');
        }
    }
    async findByEmailWithPassword(email) {
        try {
            const user = await this.userModel
                .findOne({ email: email.toLowerCase() })
                .select('+password')
                .exec();
            return user;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to find user by email');
        }
    }
    async findById(id) {
        try {
            const user = await this.userModel
                .findById(id)
                .select('-password')
                .exec();
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            return user;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            if (error.name === 'CastError') {
                throw new common_1.BadRequestException('Invalid user ID format');
            }
            throw new common_1.InternalServerErrorException('Failed to find user');
        }
    }
    async findAll(query) {
        try {
            const { page = 1, limit = 10, search = '', sortBy = 'createdAt', sortOrder = 'desc' } = query;
            const skip = (page - 1) * limit;
            let searchQuery = {};
            if (search) {
                searchQuery = {
                    $or: [
                        { name: { $regex: search, $options: 'i' } },
                        { email: { $regex: search, $options: 'i' } },
                        { phone: { $regex: search, $options: 'i' } },
                    ],
                };
            }
            const sortObject = { [sortBy]: sortOrder === 'asc' ? 1 : -1 };
            const [users, total] = await Promise.all([
                this.userModel
                    .find(searchQuery)
                    .select('-password')
                    .skip(skip)
                    .limit(limit)
                    .sort(sortObject)
                    .exec(),
                this.userModel.countDocuments(searchQuery).exec(),
            ]);
            const totalPages = Math.ceil(total / limit);
            return {
                users: users,
                total,
                page,
                limit,
                totalPages,
            };
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to fetch users');
        }
    }
    async updateUser(id, updateUserDto) {
        try {
            const existingUser = await this.userModel.findById(id);
            if (!existingUser) {
                throw new common_1.NotFoundException('User not found');
            }
            if (updateUserDto.email && updateUserDto.email.toLowerCase() !== existingUser.email) {
                const emailExists = await this.userModel.findOne({
                    email: updateUserDto.email.toLowerCase(),
                    _id: { $ne: id }
                });
                if (emailExists) {
                    throw new common_1.ConflictException('Email is already taken by another user');
                }
            }
            const updateData = {
                ...updateUserDto,
                ...(updateUserDto.email && { email: updateUserDto.email.toLowerCase() }),
            };
            const updatedUser = await this.userModel
                .findByIdAndUpdate(id, updateData, {
                new: true,
                runValidators: true
            })
                .select('-password')
                .exec();
            if (!updatedUser) {
                throw new common_1.NotFoundException('User not found');
            }
            return updatedUser;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException) {
                throw error;
            }
            if (error.name === 'ValidationError') {
                const messages = Object.values(error.errors).map((val) => val.message);
                throw new common_1.BadRequestException(messages.join(', '));
            }
            if (error.name === 'CastError') {
                throw new common_1.BadRequestException('Invalid user ID format');
            }
            throw new common_1.InternalServerErrorException('Failed to update user');
        }
    }
    async deleteUser(id) {
        try {
            const deletedUser = await this.userModel
                .findByIdAndDelete(id)
                .select('-password')
                .exec();
            if (!deletedUser) {
                throw new common_1.NotFoundException('User not found');
            }
            return deletedUser;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            if (error.name === 'CastError') {
                throw new common_1.BadRequestException('Invalid user ID format');
            }
            throw new common_1.InternalServerErrorException('Failed to delete user');
        }
    }
    async updatePassword(id, newPassword) {
        try {
            const saltRounds = 10;
            const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
            const updatedUser = await this.userModel
                .findByIdAndUpdate(id, { password: hashedPassword }, { new: true })
                .exec();
            if (!updatedUser) {
                throw new common_1.NotFoundException('User not found');
            }
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            if (error.name === 'CastError') {
                throw new common_1.BadRequestException('Invalid user ID format');
            }
            throw new common_1.InternalServerErrorException('Failed to update password');
        }
    }
    async updateResetToken(email, token, otp, expires) {
        try {
            const updatedUser = await this.userModel
                .findOneAndUpdate({ email: email.toLowerCase() }, {
                resetPasswordToken: token,
                resetPasswordOTP: otp,
                resetPasswordExpires: expires
            }, { new: true })
                .exec();
            if (!updatedUser) {
                throw new common_1.NotFoundException('User not found');
            }
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to update reset token');
        }
    }
    async clearResetToken(email) {
        try {
            const updatedUser = await this.userModel
                .findOneAndUpdate({ email: email.toLowerCase() }, {
                resetPasswordToken: null,
                resetPasswordOTP: null,
                resetPasswordExpires: null
            }, { new: true })
                .exec();
            if (!updatedUser) {
                throw new common_1.NotFoundException('User not found');
            }
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to clear reset token');
        }
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(users_schema_1.Users.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], UsersService);
//# sourceMappingURL=users.service.js.map