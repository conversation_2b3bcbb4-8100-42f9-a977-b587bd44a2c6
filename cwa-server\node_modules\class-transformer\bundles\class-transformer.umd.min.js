!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).ClassTransformer={})}(this,(function(t){"use strict";var n;t.TransformationType=void 0,(n=t.TransformationType||(t.TransformationType={}))[n.PLAIN_TO_CLASS=0]="PLAIN_TO_CLASS",n[n.CLASS_TO_PLAIN=1]="CLASS_TO_PLAIN",n[n.CLASS_TO_CLASS=2]="CLASS_TO_CLASS";var o=new(function(){function n(){this._typeMetadatas=new Map,this._transformMetadatas=new Map,this._exposeMetadatas=new Map,this._excludeMetadatas=new Map,this._ancestorsMap=new Map}return n.prototype.addTypeMetadata=function(t){this._typeMetadatas.has(t.target)||this._typeMetadatas.set(t.target,new Map),this._typeMetadatas.get(t.target).set(t.propertyName,t)},n.prototype.addTransformMetadata=function(t){this._transformMetadatas.has(t.target)||this._transformMetadatas.set(t.target,new Map),this._transformMetadatas.get(t.target).has(t.propertyName)||this._transformMetadatas.get(t.target).set(t.propertyName,[]),this._transformMetadatas.get(t.target).get(t.propertyName).push(t)},n.prototype.addExposeMetadata=function(t){this._exposeMetadatas.has(t.target)||this._exposeMetadatas.set(t.target,new Map),this._exposeMetadatas.get(t.target).set(t.propertyName,t)},n.prototype.addExcludeMetadata=function(t){this._excludeMetadatas.has(t.target)||this._excludeMetadatas.set(t.target,new Map),this._excludeMetadatas.get(t.target).set(t.propertyName,t)},n.prototype.findTransformMetadatas=function(n,o,e){return this.findMetadatas(this._transformMetadatas,n,o).filter((function(n){return!n.options||(!0===n.options.toClassOnly&&!0===n.options.toPlainOnly||(!0===n.options.toClassOnly?e===t.TransformationType.CLASS_TO_CLASS||e===t.TransformationType.PLAIN_TO_CLASS:!0!==n.options.toPlainOnly||e===t.TransformationType.CLASS_TO_PLAIN))}))},n.prototype.findExcludeMetadata=function(t,n){return this.findMetadata(this._excludeMetadatas,t,n)},n.prototype.findExposeMetadata=function(t,n){return this.findMetadata(this._exposeMetadatas,t,n)},n.prototype.findExposeMetadataByCustomName=function(t,n){return this.getExposedMetadatas(t).find((function(t){return t.options&&t.options.name===n}))},n.prototype.findTypeMetadata=function(t,n){return this.findMetadata(this._typeMetadatas,t,n)},n.prototype.getStrategy=function(t){var n=this._excludeMetadatas.get(t),o=n&&n.get(void 0),e=this._exposeMetadatas.get(t),r=e&&e.get(void 0);return o&&r||!o&&!r?"none":o?"excludeAll":"exposeAll"},n.prototype.getExposedMetadatas=function(t){return this.getMetadata(this._exposeMetadatas,t)},n.prototype.getExcludedMetadatas=function(t){return this.getMetadata(this._excludeMetadatas,t)},n.prototype.getExposedProperties=function(n,o){return this.getExposedMetadatas(n).filter((function(n){return!n.options||(!0===n.options.toClassOnly&&!0===n.options.toPlainOnly||(!0===n.options.toClassOnly?o===t.TransformationType.CLASS_TO_CLASS||o===t.TransformationType.PLAIN_TO_CLASS:!0!==n.options.toPlainOnly||o===t.TransformationType.CLASS_TO_PLAIN))})).map((function(t){return t.propertyName}))},n.prototype.getExcludedProperties=function(n,o){return this.getExcludedMetadatas(n).filter((function(n){return!n.options||(!0===n.options.toClassOnly&&!0===n.options.toPlainOnly||(!0===n.options.toClassOnly?o===t.TransformationType.CLASS_TO_CLASS||o===t.TransformationType.PLAIN_TO_CLASS:!0!==n.options.toPlainOnly||o===t.TransformationType.CLASS_TO_PLAIN))})).map((function(t){return t.propertyName}))},n.prototype.clear=function(){this._typeMetadatas.clear(),this._exposeMetadatas.clear(),this._excludeMetadatas.clear(),this._ancestorsMap.clear()},n.prototype.getMetadata=function(t,n){var o,e=t.get(n);e&&(o=Array.from(e.values()).filter((function(t){return void 0!==t.propertyName})));for(var r=[],a=0,i=this.getAncestors(n);a<i.length;a++){var s=i[a],p=t.get(s);if(p){var f=Array.from(p.values()).filter((function(t){return void 0!==t.propertyName}));r.push.apply(r,f)}}return r.concat(o||[])},n.prototype.findMetadata=function(t,n,o){var e=t.get(n);if(e){var r=e.get(o);if(r)return r}for(var a=0,i=this.getAncestors(n);a<i.length;a++){var s=i[a],p=t.get(s);if(p){var f=p.get(o);if(f)return f}}},n.prototype.findMetadatas=function(t,n,o){var e,r=t.get(n);r&&(e=r.get(o));for(var a=[],i=0,s=this.getAncestors(n);i<s.length;i++){var p=s[i],f=t.get(p);f&&f.has(o)&&a.push.apply(a,f.get(o))}return a.slice().reverse().concat((e||[]).slice().reverse())},n.prototype.getAncestors=function(t){if(!t)return[];if(!this._ancestorsMap.has(t)){for(var n=[],o=Object.getPrototypeOf(t.prototype.constructor);void 0!==o.prototype;o=Object.getPrototypeOf(o.prototype.constructor))n.push(o);this._ancestorsMap.set(t,n)}return this._ancestorsMap.get(t)},n}());var e=function(t,n,o){if(o||2===arguments.length)for(var e,r=0,a=n.length;r<a;r++)!e&&r in n||(e||(e=Array.prototype.slice.call(n,0,r)),e[r]=n[r]);return t.concat(e||Array.prototype.slice.call(n))};var r=function(){function n(t,n){this.transformationType=t,this.options=n,this.recursionStack=new Set}return n.prototype.transform=function(n,e,r,a,i,s){var p,f=this;if(void 0===s&&(s=0),Array.isArray(e)||e instanceof Set){var u=a&&this.transformationType===t.TransformationType.PLAIN_TO_CLASS?function(t){var n=new t;return n instanceof Set||"push"in n?n:[]}(a):[];return e.forEach((function(o,e){var a=n?n[e]:void 0;if(f.options.enableCircularCheck&&f.isCircular(o))f.transformationType===t.TransformationType.CLASS_TO_CLASS&&(u instanceof Set?u.add(o):u.push(o));else{var i=void 0;if("function"!=typeof r&&r&&r.options&&r.options.discriminator&&r.options.discriminator.property&&r.options.discriminator.subTypes){if(f.transformationType===t.TransformationType.PLAIN_TO_CLASS){i=r.options.discriminator.subTypes.find((function(t){return t.name===o[r.options.discriminator.property]}));var p={newObject:u,object:o,property:void 0},c=r.typeFunction(p);i=void 0===i?c:i.value,r.options.keepDiscriminatorProperty||delete o[r.options.discriminator.property]}f.transformationType===t.TransformationType.CLASS_TO_CLASS&&(i=o.constructor),f.transformationType===t.TransformationType.CLASS_TO_PLAIN&&(o[r.options.discriminator.property]=r.options.discriminator.subTypes.find((function(t){return t.value===o.constructor})).name)}else i=r;var d=f.transform(a,o,i,void 0,o instanceof Map,s+1);u instanceof Set?u.add(d):u.push(d)}})),u}if(r!==String||i){if(r!==Number||i){if(r!==Boolean||i){if((r===Date||e instanceof Date)&&!i)return e instanceof Date?new Date(e.valueOf()):null==e?e:new Date(e);if(("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof global?global:"undefined"!=typeof window?window:"undefined"!=typeof self?self:void 0).Buffer&&(r===Buffer||e instanceof Buffer)&&!i)return null==e?e:Buffer.from(e);if(null===(p=e)||"object"!=typeof p||"function"!=typeof p.then||i){if(i||null===e||"object"!=typeof e||"function"!=typeof e.then){if("object"==typeof e&&null!==e){r||e.constructor===Object||(Array.isArray(e)||e.constructor!==Array)&&(r=e.constructor),!r&&n&&(r=n.constructor),this.options.enableCircularCheck&&this.recursionStack.add(e);var c=this.getKeys(r,e,i),d=n||{};n||this.transformationType!==t.TransformationType.PLAIN_TO_CLASS&&this.transformationType!==t.TransformationType.CLASS_TO_CLASS||(d=i?new Map:r?new r:{});for(var l=function(a){if("__proto__"===a||"constructor"===a)return"continue";var p=a,f=a,u=a;if(!y.options.ignoreDecorators&&r)if(y.transformationType===t.TransformationType.PLAIN_TO_CLASS)(c=o.findExposeMetadataByCustomName(r,a))&&(u=c.propertyName,f=c.propertyName);else if(y.transformationType===t.TransformationType.CLASS_TO_PLAIN||y.transformationType===t.TransformationType.CLASS_TO_CLASS){var c;(c=o.findExposeMetadata(r,a))&&c.options&&c.options.name&&(f=c.options.name)}var l=void 0;l=y.transformationType===t.TransformationType.PLAIN_TO_CLASS?e[p]:e instanceof Map?e.get(p):e[p]instanceof Function?e[p]():e[p];var T=void 0,m=l instanceof Map;if(r&&i)T=r;else if(r){var h=o.findTypeMetadata(r,u);if(h){var v={newObject:d,object:e,property:u},S=h.typeFunction?h.typeFunction(v):h.reflectedType;h.options&&h.options.discriminator&&h.options.discriminator.property&&h.options.discriminator.subTypes?e[p]instanceof Array?T=h:(y.transformationType===t.TransformationType.PLAIN_TO_CLASS&&(T=void 0===(T=h.options.discriminator.subTypes.find((function(t){if(l&&l instanceof Object&&h.options.discriminator.property in l)return t.name===l[h.options.discriminator.property]})))?S:T.value,h.options.keepDiscriminatorProperty||l&&l instanceof Object&&h.options.discriminator.property in l&&delete l[h.options.discriminator.property]),y.transformationType===t.TransformationType.CLASS_TO_CLASS&&(T=l.constructor),y.transformationType===t.TransformationType.CLASS_TO_PLAIN&&l&&(l[h.options.discriminator.property]=h.options.discriminator.subTypes.find((function(t){return t.value===l.constructor})).name)):T=S,m=m||h.reflectedType===Map}else if(y.options.targetMaps)y.options.targetMaps.filter((function(t){return t.target===r&&!!t.properties[u]})).forEach((function(t){return T=t.properties[u]}));else if(y.options.enableImplicitConversion&&y.transformationType===t.TransformationType.PLAIN_TO_CLASS){var _=Reflect.getMetadata("design:type",r.prototype,u);_&&(T=_)}}var g=Array.isArray(e[p])?y.getReflectedType(r,u):void 0,A=n?n[p]:void 0;if(d.constructor.prototype){var M=Object.getOwnPropertyDescriptor(d.constructor.prototype,f);if((y.transformationType===t.TransformationType.PLAIN_TO_CLASS||y.transformationType===t.TransformationType.CLASS_TO_CLASS)&&(M&&!M.set||d[f]instanceof Function))return"continue"}if(y.options.enableCircularCheck&&y.isCircular(l)){if(y.transformationType===t.TransformationType.CLASS_TO_CLASS){L=l;(void 0!==(L=y.applyCustomTransformations(L,r,a,e,y.transformationType))||y.options.exposeUnsetFields)&&(d instanceof Map?d.set(f,L):d[f]=L)}}else{var C=y.transformationType===t.TransformationType.PLAIN_TO_CLASS?f:a,L=void 0;y.transformationType===t.TransformationType.CLASS_TO_PLAIN?(L=e[C],L=y.applyCustomTransformations(L,r,C,e,y.transformationType),L=e[C]===L?l:L,L=y.transform(A,L,T,g,m,s+1)):void 0===l&&y.options.exposeDefaultValues?L=d[f]:(L=y.transform(A,l,T,g,m,s+1),L=y.applyCustomTransformations(L,r,C,e,y.transformationType)),(void 0!==L||y.options.exposeUnsetFields)&&(d instanceof Map?d.set(f,L):d[f]=L)}},y=this,T=0,m=c;T<m.length;T++){l(m[T])}return this.options.enableCircularCheck&&this.recursionStack.delete(e),d}return e}return e}return new Promise((function(t,n){e.then((function(n){return t(f.transform(void 0,n,r,void 0,void 0,s+1))}),n)}))}return null==e?e:Boolean(e)}return null==e?e:Number(e)}return null==e?e:String(e)},n.prototype.applyCustomTransformations=function(t,n,e,r,a){var i=this,s=o.findTransformMetadatas(n,e,this.transformationType);return void 0!==this.options.version&&(s=s.filter((function(t){return!t.options||i.checkVersion(t.options.since,t.options.until)}))),(s=this.options.groups&&this.options.groups.length?s.filter((function(t){return!t.options||i.checkGroups(t.options.groups)})):s.filter((function(t){return!t.options||!t.options.groups||!t.options.groups.length}))).forEach((function(n){t=n.transformFn({value:t,key:e,obj:r,type:a,options:i.options})})),t},n.prototype.isCircular=function(t){return this.recursionStack.has(t)},n.prototype.getReflectedType=function(t,n){if(t){var e=o.findTypeMetadata(t,n);return e?e.reflectedType:void 0}},n.prototype.getKeys=function(n,r,a){var i=this,s=o.getStrategy(n);"none"===s&&(s=this.options.strategy||"exposeAll");var p=[];if(("exposeAll"===s||a)&&(p=r instanceof Map?Array.from(r.keys()):Object.keys(r)),a)return p;if(this.options.ignoreDecorators&&this.options.excludeExtraneousValues&&n){var f=o.getExposedProperties(n,this.transformationType),u=o.getExcludedProperties(n,this.transformationType);p=e(e([],f,!0),u,!0)}if(!this.options.ignoreDecorators&&n){f=o.getExposedProperties(n,this.transformationType);this.transformationType===t.TransformationType.PLAIN_TO_CLASS&&(f=f.map((function(t){var e=o.findExposeMetadata(n,t);return e&&e.options&&e.options.name?e.options.name:t}))),p=this.options.excludeExtraneousValues?f:p.concat(f);var c=o.getExcludedProperties(n,this.transformationType);c.length>0&&(p=p.filter((function(t){return!c.includes(t)}))),void 0!==this.options.version&&(p=p.filter((function(t){var e=o.findExposeMetadata(n,t);return!e||!e.options||i.checkVersion(e.options.since,e.options.until)}))),p=this.options.groups&&this.options.groups.length?p.filter((function(t){var e=o.findExposeMetadata(n,t);return!e||!e.options||i.checkGroups(e.options.groups)})):p.filter((function(t){var e=o.findExposeMetadata(n,t);return!(e&&e.options&&e.options.groups&&e.options.groups.length)}))}return this.options.excludePrefixes&&this.options.excludePrefixes.length&&(p=p.filter((function(t){return i.options.excludePrefixes.every((function(n){return t.substr(0,n.length)!==n}))}))),p=p.filter((function(t,n,o){return o.indexOf(t)===n}))},n.prototype.checkVersion=function(t,n){var o=!0;return o&&t&&(o=this.options.version>=t),o&&n&&(o=this.options.version<n),o},n.prototype.checkGroups=function(t){return!t||this.options.groups.some((function(n){return t.includes(n)}))},n}(),a={enableCircularCheck:!1,enableImplicitConversion:!1,excludeExtraneousValues:!1,excludePrefixes:void 0,exposeDefaultValues:!1,exposeUnsetFields:!0,groups:void 0,ignoreDecorators:!1,strategy:void 0,targetMaps:void 0,version:void 0},i=function(){return i=Object.assign||function(t){for(var n,o=1,e=arguments.length;o<e;o++)for(var r in n=arguments[o])Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r]);return t},i.apply(this,arguments)},s=function(){function n(){}return n.prototype.instanceToPlain=function(n,o){return new r(t.TransformationType.CLASS_TO_PLAIN,i(i({},a),o)).transform(void 0,n,void 0,void 0,void 0,void 0)},n.prototype.classToPlainFromExist=function(n,o,e){return new r(t.TransformationType.CLASS_TO_PLAIN,i(i({},a),e)).transform(o,n,void 0,void 0,void 0,void 0)},n.prototype.plainToInstance=function(n,o,e){return new r(t.TransformationType.PLAIN_TO_CLASS,i(i({},a),e)).transform(void 0,o,n,void 0,void 0,void 0)},n.prototype.plainToClassFromExist=function(n,o,e){return new r(t.TransformationType.PLAIN_TO_CLASS,i(i({},a),e)).transform(n,o,void 0,void 0,void 0,void 0)},n.prototype.instanceToInstance=function(n,o){return new r(t.TransformationType.CLASS_TO_CLASS,i(i({},a),o)).transform(void 0,n,void 0,void 0,void 0,void 0)},n.prototype.classToClassFromExist=function(n,o,e){return new r(t.TransformationType.CLASS_TO_CLASS,i(i({},a),e)).transform(o,n,void 0,void 0,void 0,void 0)},n.prototype.serialize=function(t,n){return JSON.stringify(this.instanceToPlain(t,n))},n.prototype.deserialize=function(t,n,o){var e=JSON.parse(n);return this.plainToInstance(t,e,o)},n.prototype.deserializeArray=function(t,n,o){var e=JSON.parse(n);return this.plainToInstance(t,e,o)},n}();var p=new s;t.ClassTransformer=s,t.Exclude=function(t){return void 0===t&&(t={}),function(n,e){o.addExcludeMetadata({target:n instanceof Function?n:n.constructor,propertyName:e,options:t})}},t.Expose=function(t){return void 0===t&&(t={}),function(n,e){o.addExposeMetadata({target:n instanceof Function?n:n.constructor,propertyName:e,options:t})}},t.Transform=function(t,n){return void 0===n&&(n={}),function(e,r){o.addTransformMetadata({target:e.constructor,propertyName:r,transformFn:t,options:n})}},t.TransformInstanceToInstance=function(t){return function(n,o,e){var r=new s,a=e.value;e.value=function(){for(var n=[],o=0;o<arguments.length;o++)n[o]=arguments[o];var e=a.apply(this,n),i=!!e&&("object"==typeof e||"function"==typeof e)&&"function"==typeof e.then;return i?e.then((function(n){return r.instanceToInstance(n,t)})):r.instanceToInstance(e,t)}}},t.TransformInstanceToPlain=function(t){return function(n,o,e){var r=new s,a=e.value;e.value=function(){for(var n=[],o=0;o<arguments.length;o++)n[o]=arguments[o];var e=a.apply(this,n),i=!!e&&("object"==typeof e||"function"==typeof e)&&"function"==typeof e.then;return i?e.then((function(n){return r.instanceToPlain(n,t)})):r.instanceToPlain(e,t)}}},t.TransformPlainToInstance=function(t,n){return function(o,e,r){var a=new s,i=r.value;r.value=function(){for(var o=[],e=0;e<arguments.length;e++)o[e]=arguments[e];var r=i.apply(this,o),s=!!r&&("object"==typeof r||"function"==typeof r)&&"function"==typeof r.then;return s?r.then((function(o){return a.plainToInstance(t,o,n)})):a.plainToInstance(t,r,n)}}},t.Type=function(t,n){return void 0===n&&(n={}),function(e,r){var a=Reflect.getMetadata("design:type",e,r);o.addTypeMetadata({target:e.constructor,propertyName:r,reflectedType:a,typeFunction:t,options:n})}},t.classToClassFromExist=function(t,n,o){return p.classToClassFromExist(t,n,o)},t.classToPlain=function(t,n){return p.instanceToPlain(t,n)},t.classToPlainFromExist=function(t,n,o){return p.classToPlainFromExist(t,n,o)},t.deserialize=function(t,n,o){return p.deserialize(t,n,o)},t.deserializeArray=function(t,n,o){return p.deserializeArray(t,n,o)},t.instanceToInstance=function(t,n){return p.instanceToInstance(t,n)},t.instanceToPlain=function(t,n){return p.instanceToPlain(t,n)},t.plainToClass=function(t,n,o){return p.plainToInstance(t,n,o)},t.plainToClassFromExist=function(t,n,o){return p.plainToClassFromExist(t,n,o)},t.plainToInstance=function(t,n,o){return p.plainToInstance(t,n,o)},t.serialize=function(t,n){return p.serialize(t,n)},Object.defineProperty(t,"__esModule",{value:!0})}));
//# sourceMappingURL=class-transformer.umd.min.js.map
