{"version": 3, "file": "transform.decorator.js", "sourceRoot": "", "sources": ["../../../src/decorators/transform.decorator.ts"], "names": [], "mappings": ";;;AAAA,wCAAoD;AAGpD;;;;GAIG;AACH,SAAgB,SAAS,CACvB,WAA+C,EAC/C,UAA4B,EAAE;IAE9B,OAAO,UAAU,MAAW,EAAE,YAA6B;QACzD,gCAAsB,CAAC,oBAAoB,CAAC;YAC1C,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAsB;YACpC,WAAW;YACX,OAAO;SACR,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAZD,8BAYC", "sourcesContent": ["import { defaultMetadataStorage } from '../storage';\nimport { TransformFnParams, TransformOptions } from '../interfaces';\n\n/**\n * Defines a custom logic for value transformation.\n *\n * Can be applied to properties only.\n */\nexport function Transform(\n  transformFn: (params: TransformFnParams) => any,\n  options: TransformOptions = {}\n): PropertyDecorator {\n  return function (target: any, propertyName: string | Symbol): void {\n    defaultMetadataStorage.addTransformMetadata({\n      target: target.constructor,\n      propertyName: propertyName as string,\n      transformFn,\n      options,\n    });\n  };\n}\n"]}