{"version": 3, "file": "type-metadata.interface.js", "sourceRoot": "", "sources": ["../../../../src/interfaces/metadata/type-metadata.interface.ts"], "names": [], "mappings": "", "sourcesContent": ["import { TypeHelpOptions, TypeOptions } from '..';\n\n/**\n * This object represents metadata assigned to a property via the @Type decorator.\n */\nexport interface TypeMetadata {\n  target: Function;\n\n  /**\n   * The property name this metadata belongs to on the target (property only).\n   */\n  propertyName: string;\n\n  /**\n   * The type guessed from assigned Reflect metadata ('design:type')\n   */\n  reflectedType: any;\n\n  /**\n   * The custom function provided by the user in the @Type decorator which\n   * returns the target type for the transformation.\n   */\n  typeFunction: (options?: TypeHelpOptions) => Function;\n\n  /**\n   * Options passed to the @Type operator for this property.\n   */\n  options: TypeOptions;\n}\n"]}