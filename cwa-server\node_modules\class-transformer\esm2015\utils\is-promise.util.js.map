{"version": 3, "file": "is-promise.util.js", "sourceRoot": "", "sources": ["../../../src/utils/is-promise.util.ts"], "names": [], "mappings": "AAAA,MAAM,UAAU,SAAS,CAAI,CAAM;IACjC,OAAO,CAAC,KAAK,IAAI,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,CAAC,IAAI,KAAK,UAAU,CAAC;AAC7E,CAAC", "sourcesContent": ["export function isPromise<T>(p: any): p is Promise<T> {\n  return p !== null && typeof p === 'object' && typeof p.then === 'function';\n}\n"]}