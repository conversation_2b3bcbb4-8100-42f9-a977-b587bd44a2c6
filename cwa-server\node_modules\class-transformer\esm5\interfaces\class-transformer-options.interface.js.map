{"version": 3, "file": "class-transformer-options.interface.js", "sourceRoot": "", "sources": ["../../../src/interfaces/class-transformer-options.interface.ts"], "names": [], "mappings": "", "sourcesContent": ["import { TargetMap } from './target-map.interface';\n\n/**\n * Options to be passed during transformation.\n */\nexport interface ClassTransformOptions {\n  /**\n   * Exclusion strategy. By default exposeAll is used, which means that it will expose all properties are transformed\n   * by default.\n   */\n  strategy?: 'excludeAll' | 'exposeAll';\n\n  /**\n   * Indicates if extraneous properties should be excluded from the value when converting a plain value to a class.\n   *\n   * This option requires that each property on the target class has at least one `@Expose` or `@Exclude` decorator\n   * assigned from this library.\n   */\n  excludeExtraneousValues?: boolean;\n\n  /**\n   * Only properties with given groups gonna be transformed.\n   */\n  groups?: string[];\n\n  /**\n   * Only properties with \"since\" > version < \"until\" gonna be transformed.\n   */\n  version?: number;\n\n  /**\n   * Excludes properties with the given prefixes. For example, if you mark your private properties with \"_\" and \"__\"\n   * you can set this option's value to [\"_\", \"__\"] and all private properties will be skipped.\n   * This works only for \"exposeAll\" strategy.\n   */\n  excludePrefixes?: string[];\n\n  /**\n   * If set to true then class transformer will ignore the effect of all @Expose and @Exclude decorators.\n   * This option is useful if you want to kinda clone your object but do not apply decorators affects.\n   *\n   * __NOTE:__ You may still have to add the decorators to make other options work.\n   */\n  ignoreDecorators?: boolean;\n\n  /**\n   * Target maps allows to set a Types of the transforming object without using @Type decorator.\n   * This is useful when you are transforming external classes, or if you already have type metadata for\n   * objects and you don't want to set it up again.\n   */\n  targetMaps?: TargetMap[];\n\n  /**\n   * If set to true then class transformer will perform a circular check. (circular check is turned off by default)\n   * This option is useful when you know for sure that your types might have a circular dependency.\n   */\n  enableCircularCheck?: boolean;\n\n  /**\n   * If set to true then class transformer will try to convert properties implicitly to their target type based on their typing information.\n   *\n   * DEFAULT: `false`\n   */\n  enableImplicitConversion?: boolean;\n\n  /**\n   * If set to true then class transformer will take default values for unprovided fields.\n   * This is useful when you convert a plain object to a class and have an optional field with a default value.\n   */\n  exposeDefaultValues?: boolean;\n\n  /**\n   * When set to true, fields with `undefined` as value will be included in class to plain transformation. Otherwise\n   * those fields will be omitted from the result.\n   *\n   * DEFAULT: `true`\n   */\n  exposeUnsetFields?: boolean;\n}\n"]}