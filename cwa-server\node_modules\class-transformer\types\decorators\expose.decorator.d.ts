import { ExposeOptions } from '../interfaces';
/**
 * Marks the given class or property as included. By default the property is included in both
 * constructorToPlain and plainToConstructor transformations. It can be limited to only one direction
 * via using the `toPlainOnly` or `toClassOnly` option.
 *
 * Can be applied to class definitions and properties.
 */
export declare function Expose(options?: ExposeOptions): PropertyDecorator & ClassDecorator;
