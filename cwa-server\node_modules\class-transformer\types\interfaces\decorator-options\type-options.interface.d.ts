import { DiscriminatorDescriptor } from './type-discriminator-descriptor.interface';
/**
 * Possible transformation options for the @Type decorator.
 */
export interface TypeOptions {
    /**
     * Optional discriminator object, when provided the property value will be
     * initialized according to the specified object.
     */
    discriminator?: DiscriminatorDescriptor;
    /**
     * Indicates whether to keep the discriminator property on the
     * transformed object or not. Disabled by default.
     *
     * @default false
     */
    keepDiscriminatorProperty?: boolean;
}
